# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

""" High-level objects for fields. """
from __future__ import annotations

# Import all constants and utilities
from collections import defaultdict
from datetime import date, datetime, time
from operator import attrgetter
from xmlrpc.client import MAXINT
import ast
import base64
import copy
import contextlib
import binascii
import enum
import itertools
import json
import logging
import uuid
import warnings

import psycopg2
import pytz
from markupsafe import Markup, escape as markup_escape
from psycopg2.extras import <PERSON><PERSON> as PsycopgJson
from difflib import get_close_matches, unified_diff
from hashlib import sha256

from ..models.utils import check_property_field_value_name
from ..netsvc import ColoredFormatter, GREEN, RED, DEFAULT, COLOR_PATTERN
from ..tools import (
    float_repr, float_round, float_compare, float_is_zero, human_size,
    OrderedSet, sql, SQL, date_utils, unique, lazy_property,
    image_process, merge_sequences, is_list_of,
    html_normalize, html_sanitize,
    DEFAULT_SERVER_DATE_FORMAT as DATE_FORMAT,
    DEFAULT_SERVER_DATETIME_FORMAT as DATETIME_FORMAT,
)
from ..tools.sql import pg_varchar
from ..tools.mimetypes import guess_mimetype
from ..tools.misc import unquote, has_list_types, Sentinel, SENTINEL
from ..tools.translate import html_translate

from odoo import SUPERUSER_ID
from odoo.exceptions import CacheMiss
from odoo.osv import expression

import typing
from odoo.api import ContextType, DomainType, IdType, NewId, M, T

# Constants
DATE_LENGTH = len(date.today().strftime(DATE_FORMAT))
DATETIME_LENGTH = len(datetime.now().strftime(DATETIME_FORMAT))

# hacky-ish way to prevent access to a field through the ORM (except for sudo mode)
NO_ACCESS='.'

IR_MODELS = (
    'ir.model', 'ir.model.data', 'ir.model.fields', 'ir.model.fields.selection',
    'ir.model.relation', 'ir.model.constraint', 'ir.module.module',
)

COMPANY_DEPENDENT_FIELDS = (
    'char', 'float', 'boolean', 'integer', 'text', 'many2one', 'date', 'datetime', 'selection', 'html'
)

_logger = logging.getLogger(__name__)
_schema = logging.getLogger(__name__[:-7] + '.schema')

NoneType = type(None)

# Import utility functions
from .utils import first, resolve_mro, determine

# Import base classes
from .base import MetaField, Field

# Import basic field types
from .basic import Boolean, Integer, Float, Monetary, _String, Char, Text, Html

# Import temporal field types
from .temporal import Date, Datetime

# Import binary field types
from .binary import Binary, Image

# Import selection field types
from .selection import Selection, Reference

# Import relational field types
from .relational import (
    _Relational, Many2one, Many2oneReference, _RelationalMulti, 
    One2many, Many2many, PrefetchMany2one, PrefetchX2many
)

# Import special field types
from .special import Id, Json

# Import properties field types
from .properties import Properties, PropertiesDefinition, Command

# Export all field classes and utilities for backward compatibility
__all__ = [
    # Constants
    'DATE_LENGTH', 'DATETIME_LENGTH', 'NO_ACCESS', 'IR_MODELS', 
    'COMPANY_DEPENDENT_FIELDS', 'NoneType',
    
    # Utility functions
    'first', 'resolve_mro', 'determine',
    
    # Base classes
    'MetaField', 'Field',
    
    # Basic field types
    'Boolean', 'Integer', 'Float', 'Monetary', '_String', 'Char', 'Text', 'Html',
    
    # Temporal field types
    'Date', 'Datetime',
    
    # Binary field types
    'Binary', 'Image',
    
    # Selection field types
    'Selection', 'Reference',
    
    # Relational field types
    '_Relational', 'Many2one', 'Many2oneReference', '_RelationalMulti',
    'One2many', 'Many2many', 'PrefetchMany2one', 'PrefetchX2many',
    
    # Special field types
    'Id', 'Json',
    
    # Properties field types
    'Properties', 'PropertiesDefinition', 'Command',
]
