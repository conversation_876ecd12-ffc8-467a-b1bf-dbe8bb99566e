# Part of Odoo. See LICENSE file for full copyright and licensing details.
r"""\
Odoo HTTP layer / WSGI application

The main duty of this module is to prepare and dispatch all http
requests to their corresponding controllers: from a raw http request
arriving on the WSGI entrypoint to a :class:`~http.Request`: arriving at
a module controller with a fully setup ORM available.

Application developers mostly know this module thanks to the
:class:`~odoo.http.Controller`: class and its companion the
:func:`~odoo.http.route`: method decorator. Together they are used to
register methods responsible of delivering web content to matching URLS.

Those two are only the tip of the iceberg, below is a call graph that
shows the various processing layers each request passes through before
ending at the @route decorated endpoint. Hopefully, this call graph and
the attached function descriptions will help you understand this module.
"""

import functools
import inspect
import json
import logging
import mimetypes
import os
import re
import threading
import time
import traceback
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from hashlib import sha512
from io import BytesIO
from os.path import join as opj
from pathlib import Path
from urllib.parse import urlparse
from zlib import adler32

import babel.core

try:
    import geoip2.database
    import geoip2.models
    import geoip2.errors
except ImportError:
    geoip2 = None

try:
    import maxminddb
except ImportError:
    maxminddb = None

import psycopg2
import werkzeug.datastructures
import werkzeug.exceptions
import werkzeug.local
import werkzeug.routing
import werkzeug.security
import werkzeug.wrappers
import werkzeug.wsgi
from werkzeug.urls import URL, url_parse, url_encode, url_quote
from werkzeug.exceptions import (HTTPException, BadRequest, Forbidden,
                                 NotFound, InternalServerError)
try:
    from werkzeug.middleware.proxy_fix import ProxyFix as ProxyFix_
    ProxyFix = functools.partial(ProxyFix_, x_for=1, x_proto=1, x_host=1)
except ImportError:
    from werkzeug.contrib.fixers import ProxyFix

try:
    from werkzeug.utils import send_file as _send_file
except ImportError:
    from ..tools._vendor.send_file import send_file as _send_file

import odoo
from ..exceptions import UserError, AccessError, AccessDenied
from ..modules.module import get_manifest
from ..modules.registry import Registry
from ..service import security, model as service_model
from ..tools import (config, consteq, file_path, get_lang, json_default,
                    parse_version, profiler, unique, exception_to_unicode)
from ..tools.func import filter_kwargs, lazy_property
from ..tools.misc import submap
from ..tools.facade import Proxy, ProxyAttr, ProxyFunc
from ..tools._vendor import sessions
from ..tools._vendor.useragents import UserAgent

_logger = logging.getLogger(__name__)

# =========================================================
# Constants
# =========================================================

# The validity duration of a preflight response, one day.
CORS_MAX_AGE = 60 * 60 * 24

# The HTTP methods that do not require a CSRF validation.
CSRF_FREE_METHODS = ('GET', 'HEAD', 'OPTIONS', 'TRACE')

# The default csrf token lifetime, a salt against BREACH, one year
CSRF_TOKEN_SALT = 60 * 60 * 24 * 365

# The default lang to use when the browser doesn't specify it
DEFAULT_LANG = 'en_US'

# The dictionary to initialise a new session with.
def get_default_session():
    return {
        'context': {},  # 'lang': request.default_lang()  # must be set at runtime
        'db': None,
        'debug': '',
        'login': None,
        'uid': None,
        'session_token': None,
        '_trace': [],
    }

DEFAULT_MAX_CONTENT_LENGTH = 128 * 1024 * 1024  # 128MiB

# Two empty objects used when the geolocalization failed. They have the
# sames attributes as real countries/cities except that accessing them
# evaluates to None.
if geoip2:
    GEOIP_EMPTY_COUNTRY = geoip2.models.Country({})
    GEOIP_EMPTY_CITY = geoip2.models.City({})

# The request mimetypes that transport JSON in their body.
JSON_MIMETYPES = ('application/json', 'application/json-rpc')

# Import utility functions
from .utils import content_disposition, db_list, db_monodb, dispatch_rpc, route

# Import exception classes
from .utils import RegistryError, SessionExpiredException

# Import request and response classes
from .request import HTTPRequest, Request
from .response import _Response, Headers, ResponseCacheControl, ResponseStream, Response, FutureResponse, Stream

# Import session management
from .session import FilesystemSessionStore, Session

# Import routing and controller management
from .routing import Controller

# Import dispatcher classes
from .dispatcher import Dispatcher, HttpDispatcher, JsonRPCDispatcher

# Import application and WSGI handling
from .application import Application

# Import GeoIP functionality
from .geoip import GeoIP

# Export all classes and utilities for backward compatibility
__all__ = [
    # Constants
    'CORS_MAX_AGE', 'CSRF_FREE_METHODS', 'CSRF_TOKEN_SALT', 'DEFAULT_LANG',
    'DEFAULT_MAX_CONTENT_LENGTH', 'JSON_MIMETYPES',
    
    # Functions
    'get_default_session', 'content_disposition', 'db_list', 'db_monodb', 
    'dispatch_rpc', 'route',
    
    # Exception classes
    'RegistryError', 'SessionExpiredException',
    
    # Request and response classes
    'HTTPRequest', 'Request', '_Response', 'Headers', 'ResponseCacheControl',
    'ResponseStream', 'Response', 'FutureResponse', 'Stream',
    
    # Session management
    'FilesystemSessionStore', 'Session',
    
    # Routing and controller management
    'Controller',
    
    # Dispatcher classes
    'Dispatcher', 'HttpDispatcher', 'JsonRPCDispatcher',
    
    # Application and WSGI handling
    'Application',
    
    # GeoIP functionality
    'GeoIP',
]

# Global variables for backward compatibility
request = werkzeug.local.LocalProxy(lambda: _request_stack.top)
_request_stack = werkzeug.local.LocalStack()

# Dispatchers registry
_dispatchers = {}
